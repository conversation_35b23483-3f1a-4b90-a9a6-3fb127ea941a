<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>Portal PWB - Part Analysis</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="Portal PWB" name="description" />
    <meta content="PWB" name="author" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <link rel="shortcut icon" href="<?php echo e(asset('assets/images/logo-small.png')); ?>">
    <!-- Styles -->
    <?php echo app('Illuminate\Foundation\Vite')([
    'resources/assets/css/bootstrap.min.css',
    'resources/assets/css/icons.min.css',
    'resources/assets/css/app.min.css',
    'resources/css/app.css',
    'resources/css/superadmin-dashboard.css',
    'resources/css/superadmin-scaling.css'
    ]); ?>
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css">
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>

    <style>
        .w-fit-content {
            width: fit-content;
        }
        .shadow-kit {
            border: 1px;
            border-radius: 0.5rem;
            background-color: #fff;
        }
        
        .status-ready {
            background-color: #97f784;
            color: #343a40;
        }
        
        .status-not-ready {
            background-color: #eb3124;
            color: white;
        }
        
        .classification-high-demand {
            background-color: #eb3124;
            color: white;
        }
        
        .classification-stable {
            background-color: #97f784;
            color: #343a40;
        }
        
        .classification-low-demand {
            background-color: #feff8c;
            color: #343a40;
        }
        
        .classification-seasonal {
            background-color: #58c0f6;
            color: white;
        }
        
        .classification-overstocked {
            background-color: #510968;
            color: white;
        }
        
        .classification-uncategorized {
            background-color: #6c757d;
            color: white;
        }
        
        .classification-data-insufficient {
            background-color: #f8f9fa;
            color: #343a40;
            border: 1px solid #dee2e6;
        }
        
        .table-responsive {
            max-height: 70vh;
            overflow-y: auto;
        }
        
        .sticky-header {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: #f8f9fa;
        }

        /* Mobile menu styling */
        .header-right {
            position: fixed;
            top: 0;
            right: -100%;
            width: 85%;
            max-width: 300px;
            height: auto;
            max-height: 80vh;
            background-color: #fff;
            box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
            padding: 15px;
            z-index: 999;
            overflow-y: auto;
            transition: right 0.3s ease;
            flex-direction: column;
            border-radius: 0 0 0 10px;
        }

        .mobile-menu-close {
            display: block;
        }

        .header-right.active {
            right: 0;
        }

        .mobile-menu-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 998;
            display: none;
        }

        .mobile-menu-overlay.active {
            display: block;
        }

        @media (max-width: 992px) {
            .header-right {
                display: flex;
            }
        }

        @media (min-width: 993px) {
            .header-right {
                position: static;
                width: auto;
                max-width: none;
                height: auto;
                max-height: none;
                background-color: transparent;
                box-shadow: none;
                padding: 0;
                overflow-y: visible;
                transition: none;
                flex-direction: row;
                border-radius: 0;
            }

            .mobile-menu-close {
                display: none;
            }
        }
    </style>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-refresh data when filters change
        const siteSelect = document.getElementById('site-select');
        const startDateInput = document.getElementById('start-date');
        const endDateInput = document.getElementById('end-date');
        
        function refreshData() {
            const siteId = siteSelect.value;
            const startDate = startDateInput.value;
            const endDate = endDateInput.value;
            
            if (!siteId || !startDate || !endDate) {
                return;
            }
            
            // Show loading
            const tableBody = document.getElementById('analysis-table-body');
            const summaryInfo = document.getElementById('summary-info');
            
            tableBody.innerHTML = '<tr><td colspan="8" class="text-center"><i class="mdi mdi-loading mdi-spin"></i> Memuat data...</td></tr>';
            summaryInfo.innerHTML = '<i class="mdi mdi-loading mdi-spin"></i> Memuat...';
            
            // Fetch data
            fetch(`<?php echo e(route('superadmin.part-analysis')); ?>?site_id=${siteId}&start_date=${startDate}&end_date=${endDate}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                updateTable(data);
                updateSummary(data);
            })
            .catch(error => {
                console.error('Error:', error);
                tableBody.innerHTML = '<tr><td colspan="8" class="text-center text-danger">Terjadi kesalahan saat memuat data</td></tr>';
                summaryInfo.innerHTML = 'Error';
            });
        }
        
        function updateTable(response) {
            const tableBody = document.getElementById('analysis-table-body');
            
            if (!response.data || response.data.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">Tidak ada data untuk ditampilkan</td></tr>';
                return;
            }
            
            let html = '';
            response.data.forEach((item, index) => {
                const statusClass = item.status === 'Ready' ? 'status-ready' : 'status-not-ready';
                const classificationClass = getClassificationClass(item.analysis_description);
                
                html += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${item.part_name}</td>
                        <td>${item.part_code}</td>
                        <td class="text-end">${item.current_stock.toLocaleString('id-ID')}</td>
                        <td class="text-end">${item.in_stock.toLocaleString('id-ID')}</td>
                        <td class="text-end">${item.out_stock.toLocaleString('id-ID')}</td>
                        <td><span class="badge ${statusClass}">${item.status}</span></td>
                        <td><span class="badge ${classificationClass}">${item.analysis_description}</span></td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = html;
        }
        
        function updateSummary(response) {
            const summaryInfo = document.getElementById('summary-info');
            summaryInfo.innerHTML = `
                Total Parts: ${response.total_parts} | 
                Periode: ${response.period_start} - ${response.period_end} | 
                Pengelompokan: Mingguan
            `;
        }
        
        function getClassificationClass(classification) {
            const classMap = {
                'High Demand': 'classification-high-demand',
                'Stable': 'classification-stable',
                'Low Demand': 'classification-low-demand',
                'Seasonal': 'classification-seasonal',
                'Overstocked': 'classification-overstocked',
                'Uncategorized': 'classification-uncategorized',
                'Data Insufficient': 'classification-data-insufficient'
            };
            
            return classMap[classification] || 'classification-uncategorized';
        }
        
        // Add event listeners
        siteSelect.addEventListener('change', refreshData);
        startDateInput.addEventListener('change', refreshData);
        endDateInput.addEventListener('change', refreshData);
    });
    </script>

    <!-- Vite JS Resources -->
    <?php echo app('Illuminate\Foundation\Vite')([
    'resources/js/superadmin-scaling.js',
    'resources/js/superadmin-mobile-menu.js'
    ]); ?>
</head>

<body>
    <!-- Dashboard Container -->
    <div class="dashboard-container">
        <!-- Login Theme Header -->
        <header class="login-theme-header">
            <!-- Company Logo (Visible on all devices) -->
            <div class="company-logo">
                <img src="<?php echo e(asset('assets/images/logo-small.png')); ?>" alt="PWB Logo">
                <h1 class="company-name">PT. PUTERA WIBOWO BORNEO</h1>
            </div>

            <!-- Mobile Header Top (Only visible on mobile) -->
            <div class="header-top">
                <div class="company-logo">
                    <img src="<?php echo e(asset('assets/images/logo-small.png')); ?>" alt="PWB Logo">
                    <h1 class="company-name">PT. PUTERA WIBOWO BORNEO</h1>
                </div>

                <!-- Mobile Menu Toggle Button -->
                <button type="button" class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="mdi mdi-menu"></i>
                </button>
            </div>

            <!-- Mobile Menu Overlay -->
            <div class="mobile-menu-overlay" id="mobileMenuOverlay"></div>

            <!-- Header Right (Navigation) -->
            <div class="header-right" id="mobileMenu">
                <!-- Mobile Menu Close Button (Only visible on mobile) -->
                <button type="button" class="mobile-menu-close" id="mobileMenuClose">
                    <i class="mdi mdi-close"></i>
                </button>

                <div class="nav-links">
                    <a href="<?php echo e(route('superadmin.dashboard')); ?>" class="nav-link">
                        <i class="mdi mdi-view-dashboard"></i> <span>Dashboard</span>
                    </a>
                    <a href="<?php echo e(route('superadmin.invoices')); ?>" class="nav-link">
                        <i class="mdi mdi-file-document-outline"></i> <span>Account Receivable</span>
                    </a>
                    <a href="<?php echo e(route('superadmin.parts')); ?>" class="nav-link">
                        <i class="mdi mdi-package-variant"></i> <span>Part</span>
                    </a>
                    <a href="<?php echo e(route('superadmin.part-analysis')); ?>" class="nav-link active">
                        <i class="mdi mdi-chart-line"></i> <span>Part Analysis</span>
                    </a>
                    <a href="<?php echo e(route('superadmin.price-list')); ?>" class="nav-link">
                        <i class="mdi mdi-tag-multiple"></i> <span>Price List</span>
                    </a>
                    <a href="<?php echo e(route('logout')); ?>" class="nav-link nav-link-danger">
                        <i class="mdi mdi-logout"></i> <span>Logout</span>
                    </a>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="content">
            <div class="container-fluid mt-3">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card shadow-kit">
                            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0 font-bold text-uppercase text-white">Part Analysis</h5>
                                <div id="summary-info" class="text-white" style="font-size: 11px;">
                                    <?php if(isset($analysisData) && !empty($analysisData['data'])): ?>
                                        Total Parts: <?php echo e($analysisData['total_parts']); ?> | 
                                        Periode: <?php echo e($analysisData['period_start']); ?> - <?php echo e($analysisData['period_end']); ?> | 
                                        Pengelompokan: Mingguan
                                    <?php else: ?>
                                        Pilih site dan tanggal untuk melihat analisis
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <!-- Filter Section -->
                            <div class="card-body border-bottom">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <label for="site-select" class="form-label">Site</label>
                                        <select class="form-select" id="site-select" name="site_id">
                                            <option value="">Pilih Site</option>
                                            <?php $__currentLoopData = $sites; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $site): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($site->site_id); ?>" <?php echo e($selectedSite == $site->site_id ? 'selected' : ''); ?>>
                                                    <?php echo e($site->site_name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="start-date" class="form-label">Tanggal Mulai</label>
                                        <input type="date" class="form-control" id="start-date" name="start_date" value="<?php echo e($startDate); ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="end-date" class="form-label">Tanggal Akhir</label>
                                        <input type="date" class="form-control" id="end-date" name="end_date" value="<?php echo e($endDate); ?>">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Table Section -->
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover w-100">
                                        <thead class="sticky-header" style="font-size: 11px;">
                                            <tr>
                                                <th>No</th>
                                                <th>Part Name</th>
                                                <th>Part Code</th>
                                                <th>Current Stock</th>
                                                <th>In Stock</th>
                                                <th>Out Stock</th>
                                                <th>Status</th>
                                                <th>Analysis Description</th>
                                            </tr>
                                        </thead>
                                        <tbody id="analysis-table-body" style="font-size: 11px;">
                                            <?php if(isset($analysisData) && !empty($analysisData['data'])): ?>
                                                <?php $__currentLoopData = $analysisData['data']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($index + 1); ?></td>
                                                    <td><?php echo e($item['part_name']); ?></td>
                                                    <td><?php echo e($item['part_code']); ?></td>
                                                    <td class="text-end"><?php echo e(number_format($item['current_stock'], 0, ',', '.')); ?></td>
                                                    <td class="text-end"><?php echo e(number_format($item['in_stock'], 0, ',', '.')); ?></td>
                                                    <td class="text-end"><?php echo e(number_format($item['out_stock'], 0, ',', '.')); ?></td>
                                                    <td>
                                                        <span class="badge <?php echo e($item['status'] === 'Ready' ? 'status-ready' : 'status-not-ready'); ?>">
                                                            <?php echo e($item['status']); ?>

                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php
                                                            $classificationClass = '';
                                                            switch($item['analysis_description']) {
                                                                case 'High Demand':
                                                                    $classificationClass = 'classification-high-demand';
                                                                    break;
                                                                case 'Stable':
                                                                    $classificationClass = 'classification-stable';
                                                                    break;
                                                                case 'Low Demand':
                                                                    $classificationClass = 'classification-low-demand';
                                                                    break;
                                                                case 'Seasonal':
                                                                    $classificationClass = 'classification-seasonal';
                                                                    break;
                                                                case 'Overstocked':
                                                                    $classificationClass = 'classification-overstocked';
                                                                    break;
                                                                case 'Data Insufficient':
                                                                    $classificationClass = 'classification-data-insufficient';
                                                                    break;
                                                                default:
                                                                    $classificationClass = 'classification-uncategorized';
                                                            }
                                                        ?>
                                                        <span class="badge <?php echo e($classificationClass); ?>">
                                                            <?php echo e($item['analysis_description']); ?>

                                                        </span>
                                                    </td>
                                                </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="8" class="text-center text-muted">Pilih site dan tanggal untuk melihat analisis part</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
<?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/superadmin/part-analysis.blade.php ENDPATH**/ ?>