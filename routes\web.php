<?php

use App\Http\Controllers\AnalysisController;
use App\Http\Controllers\AuthController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\UserController;
use App\Http\Controllers\AuthenticationController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\EquipmentController;
use App\Http\Controllers\WarehouseController;
use App\Http\Controllers\PartController;
use App\Http\Controllers\Alokasipartsite;
use App\Http\Controllers\SiteController;
use App\Http\Controllers\SupplierController;
use App\Http\Controllers\InstockwhoController;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\LogaktivitasController;
use App\Http\Controllers\NotificationsController;
use App\Http\Controllers\OutpartController;
use App\Http\Controllers\PartmergeController;
use App\Http\Controllers\PartwithdrawalController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\RequisitionController;
use App\Http\Controllers\Sites\PengajuanController;
use App\Http\Controllers\Sites\DashboardsiteContoller;
use App\Http\Controllers\Sites\InstocksiteController;
use App\Http\Controllers\Sites\OutstocksiteController;
use App\Http\Controllers\Sites\partprioritasController;
use App\Http\Controllers\Sites\PartwithdrawsiteContoller;
use App\Http\Controllers\Sites\InventoryCardController;
use App\Http\Controllers\DailyReportController;
use App\Http\Controllers\StocktransactionController;
use App\Http\Controllers\TransactionhistoryController;
use App\Http\Controllers\UnitController;
use App\Http\Controllers\UnitTransactionController;
use App\Http\Controllers\Sales\SalesDashboardController;
use App\Http\Controllers\TokenAuthController;
use App\Http\Controllers\SuperadminController;
use App\Http\Controllers\SuperadminLogController;
use App\Http\Controllers\AdminSiteModeController;
use App\Http\Controllers\Warehouse\PenawaranManagementController;
use App\Http\Controllers\KasirController;
use App\Http\Controllers\DailyReportViewController;
use App\Http\Controllers\UnitRecapController;
use App\Http\Controllers\BacklogController;
use App\Http\Controllers\ServicePredictionController;

Route::get('', [AuthController::class, 'index']);
Route::get('/login', [AuthController::class, 'index']);
Route::post('/login', [AuthController::class, 'login'])->name('login');
Route::get('/logout', [AuthController::class, 'logout'])->name('logout');

Route::get('/daily-report-preview', [DailyReportViewController::class, 'show']);

// Token Login Routes
Route::get('/token-login', [TokenAuthController::class, 'showLoginForm'])->name('token.login.form');
Route::post('/token-login', [TokenAuthController::class, 'login'])->name('token.login');
Route::post('/generate-token', [TokenAuthController::class, 'generateToken'])->name('token.generate');
Route::get('/generate-token-form', [TokenAuthController::class, 'showTokenGenerationForm'])->name('token.generate.form');

// Admin Site Mode Selection Routes
Route::get('/adminsite/mode-select', [AdminSiteModeController::class, 'showModeSelection'])->name('adminsite.mode.select');
Route::post('/adminsite/mode-select', [AdminSiteModeController::class, 'selectMode'])->name('adminsite.mode.select.post');

// Password Reset Routes
Route::get('/password/reset', [App\Http\Controllers\PasswordResetController::class, 'showResetForm'])->name('password.reset');
Route::post('/password/reset', [App\Http\Controllers\PasswordResetController::class, 'reset'])->name('password.update');


Route::get('/password/token', [App\Http\Controllers\PasswordResetController::class, 'showTokenGenerationForm'])->name('password.token.generate');
Route::get('/password/generate-token/{employee_id}', [App\Http\Controllers\PasswordResetController::class, 'generateToken'])->name('password.generate.token');


Route::get('/notifications', [NotificationsController::class, 'index']);
Route::post('/notifications/mark-read', [NotificationsController::class, 'markAsRead']);
Route::delete('/notifications/delete/{id}', [NotificationsController::class, 'destroy']);
Route::get('/pengajuan/getcount', [NotificationsController::class, 'getcount']);

Route::get('/reports', [ReportController::class, 'index'])->name('reports.index');
Route::get('/reports/export-in-pdf', [ReportController::class, 'exportInPdf'])->name('reports.exportInPdf');
Route::get('/reports/export-out-pdf', [ReportController::class, 'exportOutPdf'])->name('reports.exportOutPdf');
Route::get('/reports/export-in-excel', [ReportController::class, 'exportInExcel'])->name('reports.exportInExcel');
Route::get('/reports/export-out-excel', [ReportController::class, 'exportOutExcel'])->name('reports.exportOutExcel');

Route::get('/transaksi', [StocktransactionController::class, 'index'])->name('transaksi.index');
Route::get('/warehouse/transactions', [StocktransactionController::class, 'warehouseIndex'])->name('warehouse.transactions.index');
Route::post('/warehouse/transactions/{id}/resolve', [StocktransactionController::class, 'warehouseResolveDiscrepancy'])->name('warehouse.transactions.resolve');
Route::delete('/warehouse/transactions/{id}', [StocktransactionController::class, 'destroy'])->name('warehouse.transactions.resolve');

// route persetujuan transaksi in stock site
Route::post('/stock-transactions/{transactionId}/adjust', [InstocksiteController::class, 'processAdjustment']);
Route::get('/transactions/site/get', [InstocksiteController::class, 'gettransactionsite'])->name('stock_transactions.confirm_post');
Route::get('/transactions/site/count', [InstocksiteController::class, 'getTransactionCount'])->name('stock_transactions.count');
Route::post('/stock-transactions/{id}/confirm', [InstocksiteController::class, 'processConfirmation'])->name('stock_transactions.confirm_post');

Route::post('/stock-transactions/{id}/adjust', [StocktransactionController::class, 'processAdjustment'])->name('stock_transactions.adjust_post'); // Hapus {id}

// seacrh umum
Route::get('/last-activities', [LogaktivitasController::class, 'getlast']);
Route::get('/log-aktivitas/search', [LogaktivitasController::class, 'search'])->name('log-aktivitas.search');

Route::get('/users/create', [UserController::class, 'create'])->name('users.create');
Route::post('/users', [UserController::class, 'store'])->name('users.store');
Route::get('/users', [UserController::class, 'index'])->name('users.index');
Route::get('/users/{employee_id}', [UserController::class, 'show'])->name('users.show');
Route::put('/users/{employee_id}', [UserController::class, 'update'])->name('users.update');
Route::delete('/users/{employee_id}', [UserController::class, 'destroy'])->name('users.destroy');

// Transaction History Routes
Route::get('/warehouse/transactions/history', [TransactionhistoryController::class, 'warehouseIndex'])->name('warehouse.transactions.history');
Route::get('/warehouse/transactions/history/data', [TransactionhistoryController::class, 'getWarehouseTransactions']);

// Route for displaying the view
Route::get('/site/transactions', [TransactionhistoryController::class, 'siteIndex'])
    ->name('site.transactions.history');

// Penggabungan Part
Route::get('/part-merge/site', [PartmergeController::class, 'indexsite'])->name('part-merge.site.index');
Route::get('/part-merge', [PartmergeController::class, 'index'])->name('part-merge.index');
Route::get('/part-merge/combined-name-autocomplete', [PartmergeController::class, 'combinedNameAutocomplete']);
Route::get('/part-merge/autocomplete', [PartmergeController::class, 'autocompletePart']);
Route::post('/part-merge/submit', [PartmergeController::class, 'submitCombinedItem']);
Route::get('/part-merge/show-merged-parts', [PartmergeController::class, 'showMergedParts']);
Route::get('/part-merge/check-parts', [PartmergeController::class, 'checkParts']);
// Route for API endpoint
Route::get('/site/transactions/history', [TransactionhistoryController::class, 'getSiteTransactions'])->name('site.transactions.history.data');


Route::get('/preview-alur', function () {
    return view('preview-alur');
});

// Debug route to check session
Route::get('/debug-session', function () {
    return response()->json([
        'role' => session('role'),
        'site_id' => session('site_id'),
        'name' => session('name'),
        'employee_id' => session('employee_id')
    ]);
});

// Debug route to check timezone configuration
Route::get('/debug-timezone', function () {
    return response()->json([
        'app_timezone' => config('app.timezone'),
        'php_timezone' => date_default_timezone_get(),
        'current_time' => now()->format('Y-m-d H:i:s T'),
        'current_time_iso' => now()->toISOString(),
        'database_timezone' => config('database.connections.mysql.timezone'),
        'env_app_timezone' => env('APP_TIMEZONE'),
        'env_db_timezone' => env('DB_TIMEZONE'),
    ]);
});

// API routes for common data
Route::get('/api/sites', function() {
    return response()->json(App\Models\Site::all());
});

// Inventory Card Routes - accessible to all site users
Route::get('/inventory-card', [InventoryCardController::class, 'index'])->name('sites.inventory-card');
Route::get('/inventory-card/data', [InventoryCardController::class, 'getInventoryData'])->name('sites.inventory-card.data');
Route::get('/inventory-card/part-types', [InventoryCardController::class, 'getPartTypes'])->name('sites.inventory-card.part-types');

// Daily Reports Routes - Only accessible with daily_report mode
Route::middleware(['access.mode:daily_report'])->group(function () {
    Route::get('/daily-reports', [DailyReportController::class, 'index'])->name('daily-reports.index');
    Route::get('/daily-reports/data', [DailyReportController::class, 'getData'])->name('daily-reports.data');
    Route::get('/daily-reports/units/search', [DailyReportController::class, 'searchUnits'])->name('daily-reports.units.search');
    Route::get('/daily-reports/parts/search', [DailyReportController::class, 'searchParts'])->name('daily-reports.parts.search');
    Route::post('/daily-reports', [DailyReportController::class, 'store'])->name('daily-reports.store');
    Route::get('/daily-reports/{id}', [DailyReportController::class, 'show'])->name('daily-reports.show');
    Route::put('/daily-reports/{id}', [DailyReportController::class, 'update'])->name('daily-reports.update');
    Route::delete('/daily-reports/{id}', [DailyReportController::class, 'destroy'])->name('daily-reports.destroy');
    Route::delete('/daily-reports/images/{imageId}', [DailyReportController::class, 'deleteImage'])->name('daily-reports.delete-image');
    Route::get('/daily-reports/jobs/list', [DailyReportController::class, 'getJobs'])->name('daily-reports.jobs.list');
    Route::post('/daily-reports/jobs', [DailyReportController::class, 'storeJob'])->name('daily-reports.jobs.store');
    Route::get('/daily-reports/technicians/list', [DailyReportController::class, 'getTechnicians'])->name('daily-reports.technicians.list');
    Route::post('/daily-reports/technicians', [DailyReportController::class, 'storeTechnician'])->name('daily-reports.technicians.store');
    Route::get('/daily-reports/export/pdf', [DailyReportController::class, 'exportPdf'])->name('daily-reports.export.pdf');
    Route::get('/daily-reports/{id}/export/pdf', [DailyReportController::class, 'exportSinglePdf'])->name('daily-reports.export.single-pdf');
    Route::get('/daily-reports/export/excel', [DailyReportController::class, 'exportExcel'])->name('daily-reports.export.excel');
});

// Unit Recap Routes - Only accessible with daily_report mode
Route::middleware(['access.mode:daily_report'])->group(function () {
    Route::get('/unit-recap', [UnitRecapController::class, 'index'])->name('unit-recap.index');
    Route::get('/unit-recap/problem-component-data', [UnitRecapController::class, 'getProblemComponentData'])->name('unit-recap.problem-component-data');
    Route::get('/unit-recap/problem-component/{problemComponent}/details', [UnitRecapController::class, 'getProblemComponentDetails'])->name('unit-recap.problem-component-details');
    Route::get('/unit-recap/units/search', [UnitRecapController::class, 'searchUnits'])->name('unit-recap.units.search');
    Route::get('/unit-recap/units/{unitId}/dates', [UnitRecapController::class, 'getUnitReportDates'])->name('unit-recap.unit-dates');
    Route::get('/unit-recap/units/{unitId}/dates/{date}/reports', [UnitRecapController::class, 'getUnitDateReports'])->name('unit-recap.unit-date-reports');
    Route::get('/unit-recap/units/{unitId}/all-reports', [UnitRecapController::class, 'getAllUnitReports'])->name('unit-recap.unit-all-reports');
    Route::get('/unit-recap/units/{unitId}/backlogs', [UnitRecapController::class, 'getUnitBacklogs'])->name('unit-recap.unit-backlogs');
});

// Service Prediction Routes - Only accessible with daily_report mode
Route::middleware(['access.mode:daily_report'])->group(function () {
    Route::get('/service-prediction/unit/{unitId}', [ServicePredictionController::class, 'getPredictedServiceDate'])->name('service-prediction.unit');
    Route::get('/service-prediction/unit-code/{unitCode}', [ServicePredictionController::class, 'getPredictedServiceDateByCode'])->name('service-prediction.unit-code');
    Route::post('/service-prediction/bulk', [ServicePredictionController::class, 'getBulkPredictedServiceDates'])->name('service-prediction.bulk');
});

// Unit Schedule Routes - Only accessible with daily_report mode
Route::middleware(['access.mode:daily_report'])->group(function () {
    Route::get('/unit-schedule', [App\Http\Controllers\UnitScheduleController::class, 'index'])->name('unit-schedule.index');
    Route::get('/unit-schedule/data', [App\Http\Controllers\UnitScheduleController::class, 'getData'])->name('unit-schedule.data');
    Route::get('/unit-schedule/export', [App\Http\Controllers\UnitScheduleController::class, 'exportExcel'])->name('unit-schedule.export');
});

// Backlog Routes - Only accessible with daily_report mode
Route::middleware(['access.mode:daily_report'])->group(function () {
    Route::get('/backlogs', [BacklogController::class, 'index'])->name('backlogs.index');
    Route::get('/backlogs/data', [BacklogController::class, 'getData'])->name('backlogs.data');
    Route::post('/backlogs', [BacklogController::class, 'store'])->name('backlogs.store');
    Route::get('/backlogs/{backlog}', [BacklogController::class, 'show'])->name('backlogs.show');
    Route::get('/backlogs/{backlog}/edit', [BacklogController::class, 'edit'])->name('backlogs.edit');
    Route::put('/backlogs/{backlog}', [BacklogController::class, 'update'])->name('backlogs.update');
    Route::delete('/backlogs/{backlog}', [BacklogController::class, 'destroy'])->name('backlogs.destroy');
    Route::get('/backlogs/units/search', [BacklogController::class, 'searchUnits'])->name('backlogs.search-units');
    Route::get('/backlogs/units/{unitCode}/open', [BacklogController::class, 'getUnitBacklogs'])->name('backlogs.unit-open');
});

Route::middleware(['admin'])->group(function () {

    // Penarikan part dari site
    Route::get('/withdrawals', [PartwithdrawalController::class, 'index'])->name('returnpart');
    Route::get('/withdrawals/get', [PartwithdrawalController::class, 'getWithdrawals']);
    Route::post('/withdrawals', [PartwithdrawalController::class, 'store']);
    Route::put('/withdrawals/{id}', [PartwithdrawalController::class, 'update']);
    Route::delete('/withdrawals/{id}', [PartwithdrawalController::class, 'destroy']);

    Route::get('/parts/details', [PartwithdrawalController::class, 'getPartDetails']);
    Route::get('/parts/suggestions', [PartwithdrawalController::class, 'getPartSuggestions']);
    Route::post('/withdrawals/confirm/{id}', [PartwithdrawalController::class, 'confirmReceipt']);
    Route::get('/withdrawals/{id}', [PartwithdrawalController::class, 'show']);


    // Inventory Card ( untuk monitoring in out dan stock setiap site)
    Route::get('/inventory', [InventoryController::class, 'index'])->name('inventori.card');
    Route::post('/inventory/get-data', [InventoryController::class, 'getData']);
    Route::post('/inventory/get-detail', [InventoryController::class, 'getDetail']);
    Route::get('/inventory/{id_site}', [InventoryController::class, 'show'])->name('inventory.show');
    Route::get('/notreadysite', [InventoryController::class, 'getNotReadySiteNames']);

    Route::get('/admin/dashboard', [DashboardController::class, 'AdminHO'])->name('adminho.dashboard');
    // Rute-rute lain yang hanya bisa diakses oleh admin
    Route::get('/adminho/users/create', [AuthenticationController::class, 'showCreateUserForm'])->name('adminho.users.create');
    Route::post('/adminho/users', [AuthenticationController::class, 'createUser'])->name('adminho.users.store');

    // OUT IN ADMIN WHO
    Route::get('/outpart', [OutpartController::class, 'index'])->name('out-parts.index');
    Route::get('/out-parts/filter', [OutpartController::class, 'filter'])->name('out-parts.filter');
    Route::post('/out-parts', [OutpartController::class, 'store'])->name('out-parts.store');
    Route::delete('/out-parts/{id}', [OutpartController::class, 'destroy'])->name('out-parts.destroy');
    Route::get('/out-parts/part-inventories', [OutpartController::class, 'getPartInventories'])->name('out-parts.part-inventories');
    Route::get('/part-inventories/autocomplete', [OutpartController::class, 'autocomplete'])->name('part-inventories.autocomplete');

    // STOCK IN ADMIN WHO
    Route::get('/instockwho', [InstockwhoController::class, 'index']);
    Route::get('/instockwho/filter', [InstockwhoController::class, 'filter']);
    Route::delete('/instockwho/{id}', [InstockwhoController::class, 'destroy']);
    Route::get('/instockwho/parts', [InstockwhoController::class, 'suggestParts']);
    Route::post('/instockwho/store', [InstockwhoController::class, 'store']);

    // IN STOCK
    Route::controller(InstockwhoController::class)->group(function () {
        Route::get('/instock', 'index')->name('instock.index');
        Route::get('/instock/get-data', 'getData')->name('instock.getData');
        Route::post('/instock', 'store')->name('instock.store');
        Route::get('/instock/search-part', 'searchPart')->name('instock.searchPart');
        Route::get('/instock/get-suppliers', 'getSuppliers')->name('instock.getSuppliers');
        Route::delete('/instockwho/{id}', 'destroy')->name('instock.destroy');
    });
    // Route::get('/instock/search-part', [InstockwhoController::class, 'searchPart'])->name('instock.searchPart');

    // Management SITE
    Route::get('/sites', [SiteController::class, 'index'])->name('sites.index');
    Route::get('/sites/data', [SiteController::class, 'getSites'])->name('sites.data');
    Route::post('/sites', [SiteController::class, 'store'])->name('sites.store');
    Route::put('/sites/{id}', [SiteController::class, 'update'])->name('sites.update');
    Route::delete('/sites/{id}', [SiteController::class, 'destroy'])->name('sites.destroy');
    Route::get('/sites/{id}', [SiteController::class, 'edit'])->name('sites.edit');

    //SUpllier
    Route::get('/suppliers', [SupplierController::class, 'index']);
    Route::get('/suppliers/data', [SupplierController::class, 'getSuppliers']);
    Route::get('/suppliers/paginated', [SupplierController::class, 'getSuppliers']);
    Route::post('/suppliers', [SupplierController::class, 'store']);
    Route::get('/suppliers/{supplierId}', [SupplierController::class, 'edit']);
    Route::put('/suppliers/{supplierId}', [SupplierController::class, 'update']);
    Route::delete('/suppliers/{supplierId}', [SupplierController::class, 'destroy']);

    // CRUD PARTS
    Route::prefix('parts')->group(function () {
        Route::get('/', [PartController::class, 'index'])->name('parts.index');
        Route::get('/data', [PartController::class, 'getPartsData'])->name('parts.data');
        Route::post('/', [PartController::class, 'store'])->name('parts.store');
        Route::put('/{code_part}', [PartController::class, 'update'])->name('parts.update');
        Route::delete('/{part_code}', [PartController::class, 'destroy'])->name('parts.destroy');
    });

    // Ubah method POST ke GET untuk filter
    Route::get('/part-group', [Alokasipartsite::class, 'index'])->name('part-group.index');
    Route::get('/part-group/filter', [Alokasipartsite::class, 'filter'])->name('part-group.filter');
    Route::get('/part-group/autocomplete', [Alokasipartsite::class, 'autocomplete'])->name('part-group.autocomplete');
    Route::post('/part-group', [Alokasipartsite::class, 'store'])->name('part-group.store');
    Route::delete('/part-group/{partInventory}', [Alokasipartsite::class, 'destroy'])->name('part-group.destroy');
    Route::get('/part-group/sites', [Alokasipartsite::class, 'getSites'])->name('part-group.sites');


    // CRUD Perlengkapan
    // Pengajuan
    Route::get('/equipment', [EquipmentController::class, 'index'])->name('equipment.index'); // Keep the name for easier reference
    Route::get('/equipment/equipments', [EquipmentController::class, 'getEquipments'])->name('equipment.equipments'); // New route for equipments
    Route::get('/equipment/stocks', [EquipmentController::class, 'getEquipmentStocks'])->name('equipment.stocks'); // New route for stocks
    Route::post('/equipment', [EquipmentController::class, 'store']);
    Route::get('/equipment/{equipment}', [EquipmentController::class, 'getEquipment']);
    Route::put('/equipment/{equipment}', [EquipmentController::class, 'update']);
    Route::delete('/equipment/{equipment}', [EquipmentController::class, 'destroy']);
    Route::post('/equipment-stock', [EquipmentController::class, 'storeStock']);
    Route::delete('/equipment-stock/{id}', [EquipmentController::class, 'destroystock'])->name('equipment-stock.destroy');

    // Tidak perlu perubahan routes tambahan, pastikan route berikut ada:
    Route::get('/equipment/equipments', [EquipmentController::class, 'getEquipments']);
    Route::get('/equipment/stocks', [EquipmentController::class, 'getEquipmentStocks']);
    Route::get('/equipment-stock/{id}', [EquipmentController::class, 'getEquipmentStock'])->name('equipment-stock.get');
    Route::put('/equipment-stock/{id}', [EquipmentController::class, 'updateEquipmentStock'])->name('equipment-stock.update');


    Route::get('/dashboard', [UserController::class, 'index']);

    // Admin HO
    Route::get('/who/dashboard', [WarehouseController::class, 'index'])->name('indexwho');

    Route::get('/requisitions/confirmation', [RequisitionController::class, 'index'])->name('requisitionsho.index');
    Route::get('/requisitions/data', [RequisitionController::class, 'getRequisitions'])->name('requisitions.data');
    Route::get('/requisitions/{id}/details', [RequisitionController::class, 'getDetails']);
    Route::post('/requisitions/update-detail', [RequisitionController::class, 'updateDetail']);
    Route::post('/requisitions/{id}/approve-all', [RequisitionController::class, 'approveAll']);

    // Log User
    Route::get('/log-aktivitas', [LogaktivitasController::class, 'index'])->name('logaktivitas.index');



    // Warehouse Penawaran Management Routes
    Route::prefix('warehouse/penawaran')->group(function () {
        Route::get('/', [PenawaranManagementController::class, 'index'])->name('warehouse.penawaran.index');
        Route::get('/{id}', [PenawaranManagementController::class, 'show'])->name('warehouse.penawaran.show');
        Route::post('/{id}/item-status', [PenawaranManagementController::class, 'updateItemStatus'])->name('warehouse.penawaran.update-item-status');
        Route::post('/{id}/all-items-status', [PenawaranManagementController::class, 'updateAllItemsStatus'])->name('warehouse.penawaran.update-all-items-status');
    });

    // Warehouse Part Analysis Routes
    Route::get('/warehouse/part-analysis', [App\Http\Controllers\Warehouse\PartAnalysisController::class, 'index'])->name('warehouse.part-analysis');

    // Warehouse Unit Schedule Routes
    Route::get('/warehouse/unit-schedule', [App\Http\Controllers\UnitScheduleController::class, 'indexHO'])->name('warehouse.unit-schedule');
    Route::get('/warehouse/unit-schedule/data', [App\Http\Controllers\UnitScheduleController::class, 'getData'])->name('warehouse.unit-schedule.data');
    Route::get('/warehouse/unit-schedule/export', [App\Http\Controllers\UnitScheduleController::class, 'exportExcel'])->name('warehouse.unit-schedule.export');

    // analisis
    Route::get('/inventory-analysis', [AnalysisController::class, 'index'])->name('inventory_analysis.index');
    Route::post('/inventory-analysis/analyze', [AnalysisController::class, 'analyze'])->name('inventory_analysis.analyze');

    // pengjuan notif
});

Route::middleware(['sales'])->group(function () {
    // Sales Dashboard
    Route::get('/sales/dashboard', [SalesDashboardController::class, 'index'])->name('sales.dashboard');
    Route::get('/sales/transactions', [SalesDashboardController::class, 'getTransactions']);
    Route::get('/sales/invoiced-transactions', [SalesDashboardController::class, 'getInvoicedTransactions']);
    Route::get('/sales/all-invoices-data', [SalesDashboardController::class, 'getAllInvoices']);
    Route::get('/sales/transactions/{id}', [SalesDashboardController::class, 'getTransaction']);
    Route::put('/sales/transactions/{id}/status', [SalesDashboardController::class, 'updateStatus']);
    Route::put('/sales/transactions/{id}/notes', [SalesDashboardController::class, 'updateSalesNotes']);
    Route::get('/sales/units', [SalesDashboardController::class, 'getUnits']);

    // Customer Sales routes
    Route::get('/sales/customer', [App\Http\Controllers\Sales\CustomerSalesController::class, 'index'])->name('sales.customer');
    Route::get('/sales/customer-data', [App\Http\Controllers\Sales\CustomerSalesController::class, 'getCustomersData'])->name('sales.customer.data');
    Route::post('/sales/customer', [App\Http\Controllers\Sales\CustomerSalesController::class, 'store'])->name('sales.customer.store');
    Route::put('/sales/customer/{id}', [App\Http\Controllers\Sales\CustomerSalesController::class, 'update'])->name('sales.customer.update');
    Route::delete('/sales/customer/{id}', [App\Http\Controllers\Sales\CustomerSalesController::class, 'destroy'])->name('sales.customer.destroy');
    Route::get('/sales/customer-search', [App\Http\Controllers\Sales\CustomerSalesController::class, 'search'])->name('sales.customer.search');
    Route::get('/sales/customer/{id}', [App\Http\Controllers\Sales\CustomerSalesController::class, 'show'])->name('sales.customer.show');

    // Penawaran routes
    Route::get('/sales/penawaran', [App\Http\Controllers\Sales\PenawaranController::class, 'index'])->name('sales.penawaran');
    Route::post('/sales/penawaran', [App\Http\Controllers\Sales\PenawaranController::class, 'store'])->name('sales.penawaran.store');
    Route::get('/sales/penawaran/search-parts', [App\Http\Controllers\Sales\PenawaranController::class, 'searchParts'])->name('sales.penawaran.search-parts');
    Route::get('/sales/penawaran/next-number', [App\Http\Controllers\Sales\PenawaranController::class, 'getNextNumber'])->name('sales.penawaran.next-number');
    Route::get('/sales/penawaran/{id}', [App\Http\Controllers\Sales\PenawaranController::class, 'show'])->name('sales.penawaran.show');
    Route::put('/sales/penawaran/{id}', [App\Http\Controllers\Sales\PenawaranController::class, 'update'])->name('sales.penawaran.update');
    Route::delete('/sales/penawaran/{id}', [App\Http\Controllers\Sales\PenawaranController::class, 'destroy'])->name('sales.penawaran.destroy');
    Route::get('/sales/penawaran/{id}/pdf', [App\Http\Controllers\Sales\PenawaranController::class, 'generatePdf'])->name('sales.penawaran.pdf');
    Route::get('/sales/penawaran/{id}/invoice', [App\Http\Controllers\Sales\PenawaranController::class, 'generateInvoice'])->name('sales.penawaran.invoice');
    Route::get('/sales/penawaran/{id}/invoice-data', [App\Http\Controllers\Sales\PenawaranController::class, 'getInvoiceData'])->name('sales.penawaran.invoice-data');
    Route::post('/sales/penawaran/save-invoice', [App\Http\Controllers\Sales\PenawaranController::class, 'saveInvoice'])->name('sales.penawaran.save-invoice');
    Route::post('/sales/penawaran/update-status', [App\Http\Controllers\Sales\PenawaranController::class, 'updateStatus'])->name('sales.penawaran.update-status');
    Route::get('/sales/penawaran/test-part/{partCode}', [App\Http\Controllers\Sales\PenawaranController::class, 'testPartData'])->name('sales.penawaran.test-part');

    // Invoice routes
    Route::get('/sales/transactions/{id}/preview-invoice', [SalesDashboardController::class, 'previewInvoice'])->name('sales.transactions.preview-invoice');
    Route::get('/sales/transactions/{id}/download-invoice', [SalesDashboardController::class, 'downloadInvoice'])->name('sales.transactions.download-invoice');
    Route::get('/sales/transactions/{id}/invoice', [App\Http\Controllers\InvoiceController::class, 'getInvoice'])->name('sales.transactions.invoice');
    Route::post('/sales/invoices', [App\Http\Controllers\InvoiceController::class, 'store'])->name('sales.invoices.store');
    Route::post('/sales/direct-invoices', [App\Http\Controllers\InvoiceController::class, 'storeDirectInvoice'])->name('sales.direct-invoices.store');
    Route::post('/sales/manual-invoices', [App\Http\Controllers\ManualInvoiceController::class, 'storeManualInvoice'])->name('sales.manual-invoices.store');
    Route::put('/sales/manual-invoices/{id}', [App\Http\Controllers\ManualInvoiceController::class, 'updateManualInvoice'])->name('sales.manual-invoices.update');
    Route::get('/sales/latest-invoice', [App\Http\Controllers\ManualInvoiceController::class, 'getLatestInvoice'])->name('sales.latest-invoice');
    Route::put('/sales/invoices/{id}', [App\Http\Controllers\InvoiceController::class, 'update'])->name('sales.invoices.update');
    Route::post('/sales/check-transactions-invoiced', [App\Http\Controllers\InvoiceController::class, 'checkTransactionsInvoiced'])->name('sales.check-transactions-invoiced');

    // New invoice routes for multi-transaction invoices
    Route::get('/sales/invoices/{id}/preview', [SalesDashboardController::class, 'previewInvoice'])->name('sales.invoices.preview');
    Route::get('/sales/invoices/{id}/download', [SalesDashboardController::class, 'downloadInvoice'])->name('sales.invoices.download');

    // Invoice payment status routes
    Route::get('/sales/invoices', [SalesDashboardController::class, 'invoices'])->name('sales.invoices');
    Route::get('/sales/invoices/create', [App\Http\Controllers\ManualInvoiceController::class, 'create'])->name('sales.invoices.create');
    Route::get('/sales/invoices/search-parts', [App\Http\Controllers\ManualInvoiceController::class, 'searchParts'])->name('sales.invoices.search-parts');
    Route::get('/sales/invoices/search-customers', [App\Http\Controllers\ManualInvoiceController::class, 'searchCustomers'])->name('sales.invoices.search-customers');
    Route::get('/sales/manual-invoices', [App\Http\Controllers\ManualInvoiceController::class, 'getManualInvoices'])->name('sales.manual-invoices');
    Route::get('/sales/manual-invoices/{id}', [App\Http\Controllers\ManualInvoiceController::class, 'getInvoiceDetails'])->name('sales.manual-invoices.details');
    Route::get('/sales/manual-invoices/{id}/print', [App\Http\Controllers\ManualInvoiceController::class, 'printInvoice'])->name('sales.manual-invoices.print');
    Route::get('/sales/all-invoices', [App\Http\Controllers\InvoiceController::class, 'allInvoices'])->name('sales.all-invoices');
    Route::post('/sales/invoices/payment-status', [App\Http\Controllers\InvoicePaymentController::class, 'updatePaymentStatus'])->name('sales.invoices.payment-status');
    Route::get('/sales/invoices/{id}', [App\Http\Controllers\InvoiceController::class, 'getInvoiceDetails'])->name('sales.invoices.details');
    Route::delete('/sales/invoices/{id}', [App\Http\Controllers\InvoiceController::class, 'destroy'])->name('sales.invoices.destroy');
    Route::get('/sales/invoices/{id}/download-attachments', [SalesDashboardController::class, 'downloadInvoiceAttachments'])->name('sales.invoices.download-attachments');

    // Removed completed invoices route



    // Part List routes
    Route::get('/sales/part-list', [App\Http\Controllers\Sales\PartListController::class, 'index'])->name('sales.part-list');
    Route::get('/sales/part-list/data', [App\Http\Controllers\Sales\PartListController::class, 'getData'])->name('sales.part-list.data');
    Route::post('/sales/part-list', [App\Http\Controllers\Sales\PartListController::class, 'store'])->name('sales.part-list.store');
    Route::put('/sales/part-list/{partCode}', [App\Http\Controllers\Sales\PartListController::class, 'update'])->name('sales.part-list.update');
    Route::get('/sales/part-list/part-types', [App\Http\Controllers\Sales\PartListController::class, 'getPartTypes'])->name('sales.part-list.part-types');
    Route::post('/sales/part-list/check-duplicate', [App\Http\Controllers\Sales\PartListController::class, 'checkDuplicate'])->name('sales.part-list.check-duplicate');
});

// Superadmin Routes
Route::middleware(['superadmin'])->group(function () {
    Route::get('/superadmin/dashboard', [SuperadminController::class, 'dashboard'])->name('superadmin.dashboard');
    Route::get('/superadmin/site-details/{siteId}', [SuperadminController::class, 'getSiteDetails'])->name('superadmin.site-details');
    Route::get('/superadmin/units-by-status/{status}', [SuperadminController::class, 'getUnitsByStatus'])->name('superadmin.units-by-status');
    Route::post('/superadmin/save-best-parts-settings', [SuperadminController::class, 'saveBestPartsSettings'])->name('superadmin.save-best-parts-settings');
    Route::get('/superadmin/best-parts-data', [SuperadminController::class, 'getBestPartsData'])->name('superadmin.best-parts-data');

    // Superadmin Part Analysis Routes
    Route::get('/superadmin/part-analysis', [App\Http\Controllers\Superadmin\PartAnalysisController::class, 'index'])->name('superadmin.part-analysis');
    Route::get('/superadmin/division-parts-detail', [SuperadminController::class, 'getDivisionPartsDetail'])->name('superadmin.division-parts-detail');
    Route::get('/superadmin/jasa-karyawan-data', [SuperadminController::class, 'getJasaKaryawanData'])->name('superadmin.jasa-karyawan-data');
    Route::get('/superadmin/sites-data', [SuperadminController::class, 'getSitesData'])->name('superadmin.sites-data');
    Route::get('/superadmin/parts', [SuperadminController::class, 'parts'])->name('superadmin.parts');
    Route::get('/superadmin/parts-data', [SuperadminController::class, 'getPartsData'])->name('superadmin.parts-data');
    Route::get('/superadmin/penawaran/{id}', [SuperadminController::class, 'getPenawaranDetail'])->name('superadmin.penawaran.detail');
    Route::get('/superadmin/price-list', [SuperadminController::class, 'priceList'])->name('superadmin.price-list');
    Route::get('/superadmin/price-list-data', [SuperadminController::class, 'getPriceListData'])->name('superadmin.price-list-data');
    Route::get('/superadmin/invoices', [SuperadminController::class, 'invoices'])->name('superadmin.invoices');
    Route::get('/superadmin/invoices-data', [SuperadminController::class, 'getInvoicesData'])->name('superadmin.invoices-data');
    Route::get('/superadmin/invoices/{id}', [SuperadminController::class, 'getInvoiceDetails'])->name('superadmin.invoices.details');
    Route::get('/superadmin/ready-po-data', [SuperadminController::class, 'getReadyPoData'])->name('superadmin.ready-po-data');
});

// Superadmin Log Routes (PIN protected)
Route::get('/superadmin/log/pin', [SuperadminLogController::class, 'showPinForm'])->name('superadmin.log.pin');
Route::post('/superadmin/log/verify-pin', [SuperadminLogController::class, 'verifyPin'])->name('superadmin.log.verify-pin');

// PIN protected routes
Route::middleware(['superadmin.pin'])->group(function () {
    Route::get('/superadmin/log', [SuperadminLogController::class, 'index'])->name('superadmin.log.index');
    Route::get('/superadmin/log/data', [SuperadminLogController::class, 'getData'])->name('superadmin.log.data');
    Route::get('/superadmin/log/logout', [SuperadminLogController::class, 'logout'])->name('superadmin.log.logout');
});

Route::middleware(['adminsite'])->group(function () {
    // Common routes for all adminsite users regardless of mode
    Route::get('/dashboardsites', [DashboardsiteContoller::class, 'index'])->name('sites.dashboard');
    Route::get('/sites/dashboard/get-stock-totals', [DashboardsiteContoller::class, 'getStockTotals'])->name('sites.dashboard.get-stock-totals');

    // Routes that require inventory mode
    Route::middleware(['access.mode:inventory'])->group(function () {
    // ======================================================================================================================================
    // INVENTORY MODE ROUTES
    // ======================================================================================================================================
    // log aktivitas
    Route::get('/logaktivitassite', [LogaktivitasController::class, 'logsite'])->name('logsite.index');
    Route::get('/logaktivitassite/data', [LogaktivitasController::class, 'logsiteData'])->name('logsite.data');

    // pengajuan
    Route::get('/withdrawalsite', [PartwithdrawsiteContoller::class, 'index'])->name('returnpart.site');
    Route::get('/withdrawalsite/get', [PartwithdrawsiteContoller::class, 'getWithdrawals']);
    Route::post('/withdrawalsite', [PartwithdrawsiteContoller::class, 'store']);
    Route::put('/withdrawalsite/{id}', [PartwithdrawsiteContoller::class, 'update']);
    Route::delete('/withdrawalsite/{id}', [PartwithdrawsiteContoller::class, 'destroy']);
    Route::get('/parts/detailsite', [PartwithdrawsiteContoller::class, 'getPartDetails']);
    Route::get('/parts/suggestionsite', [PartwithdrawsiteContoller::class, 'getPartSuggestions']);
    Route::put('/withdrawalsite/status/{id}', [PartwithdrawsiteContoller::class, 'updateStatus']);

    // PENGAJUAN PART
    Route::get('/pengajuan', [PengajuanController::class, 'index'])->name('pengajuan.index');
    Route::get('/parts/autocomplete', [PengajuanController::class, 'autocompleteParts']);
    Route::post('/pengajuan', [PengajuanController::class, 'store']);
    Route::get('/pengajuan/{id}', [PengajuanController::class, 'show']);
    Route::put('/pengajuan/{id}', [PengajuanController::class, 'update']);
    Route::delete('/pengajuan/{id}', [PengajuanController::class, 'destroy']);
    Route::get('/pengajuan/{id}/details', [PengajuanController::class, 'getRequisitionDetails']);
    Route::get('/getdata', [PengajuanController::class, 'getdata']);

    // IN STOCK
    Route::get('/siteinstock', [InstocksiteController::class, 'index'])->name('sites.instock.index');
    Route::get('/sites/instock/loadData', [InstocksiteController::class, 'loadData'])->name('sites.instock.loadData');
    Route::post('/sites/instock/create', [InstocksiteController::class, 'create'])->name('sites.instock.create');
    Route::post('/sites/instock/delete', [InstocksiteController::class, 'delete'])->name('sites.instock.delete');
    Route::get('/sites/instock/getParts', [InstocksiteController::class, 'getParts'])->name('sites.instock.getParts');

    // Out-Stock Routes
    Route::get('/outstocksite', [OutstocksiteController::class, 'index'])->name('sites.outstock.index');
    Route::get('/sites/outstock/loadData', [OutstocksiteController::class, 'loadData'])->name('sites.outstock.loadData');
    Route::post('/sites/outstock/create', [OutstocksiteController::class, 'create'])->name('sites.outstock.create');
    Route::post('/sites/outstock/delete', [OutstocksiteController::class, 'delete'])->name('sites.outstock.delete');
    Route::get('/sites/outstock/getParts', [OutstocksiteController::class, 'getParts'])->name('sites.outstock.getParts');
    Route::get('/sites/outstock/getUnits', [OutstocksiteController::class, 'getUnits'])->name('sites.outstock.getUnits');
    Route::post('/sites/outstock/update-status/{id}', [OutstocksiteController::class, 'updateStatus'])->name('sites.outstock.updateStatus');

    // PARTS UNTUK SITE
    Route::get('/partminmax', [partprioritasController::class, 'index'])->name('partgroupsite.index');
    Route::get('/partminmax/filter', [partprioritasController::class, 'filter'])->name('partgroupsite.filter');
    Route::get('/partminmax/autocomplete', [partprioritasController::class, 'autocomplete'])->name('partgroupsite.autocomplete');
    Route::post('/partminmax', [partprioritasController::class, 'store'])->name('partgroupsite.store');
    Route::delete('/partminmax/{partInventory}', [partprioritasController::class, 'destroy'])->name('partgroupsite.destroy');
    Route::get('/partminmax/sites', [partprioritasController::class, 'getSites'])->name('partgroupsite.sites');
    Route::get('/part-inventory-data', [partprioritasController::class, 'getPartInventoryData'])->name('part.inventory.data');
    Route::get('/dashboardsites', [DashboardsiteContoller::class, 'index'])->name('sites.dashboard');

    // Inventory Card Routes moved to outside middleware

    // peralatan
    Route::get('/site/equipment', [EquipmentController::class, 'getsitestockView'])->name('site.equipment.index'); // Menampilkan daftar equipment di site
    Route::get('/site/equipment/stocks/data', [EquipmentController::class, 'getsitestockData'])->name('site.equipment.data'); // Mengambil data equipment stocks (dengan filter)
    Route::get('/site/equipment-stock/{id}', [EquipmentController::class, 'getEquipmentStock'])->name('equipment-stock.show'); // Mendapatkan detail equipment stock berdasarkan ID
    Route::put('/site/equipment-stock/{id}', [EquipmentController::class, 'updateEquipmentStocksite'])->name('equipment-stock.update'); // Update status equipment stock
    Route::delete('/site/equipment-stock/{id}', [EquipmentController::class, 'destroystock'])->name('equipment-stock.destroy');

    //Unit Controller
    Route::controller(UnitController::class)->group(function () {
        Route::get('/units', 'index')->name('units.index');
        Route::post('/units', 'store');
        Route::put('/units/{unit}', 'update');
        Route::delete('/units/{unit}', 'destroy');
        Route::get('/units/{unit}', 'show');

        Route::get('/units-list', 'getUnits')->name('units.list');

        // Part-specific routes should come before routes with {unit} parameter
        Route::get('/units/parts/{id}', 'showPart')->name('units.parts.show');
        Route::put('/units/parts/{id}', 'updatePart')->name('units.parts.update');
        Route::delete('/units/parts/{id}', 'destroyPart')->name('units.parts.destroy');

        // Unit-specific routes
        Route::get('/units/{unit}/parts', 'getParts')->name('units.parts');
        Route::post('/units/{unit}/parts', 'storePart');

        // Combined Unit and Part routes
        Route::post('/units/with-part', 'storeWithPart');
        Route::put('/units/{unit}/with-part', 'updateWithPart');

        Route::get('/part-inventories/search', 'searchPartInventories');
    });

    // Unit transaction
    Route::prefix('unit-transactions')->group(function () {
        Route::get('/', [UnitTransactionController::class, 'index'])->name('unit-transactions.index');
        Route::get('/get-unit-parts/{unit}', [UnitTransactionController::class, 'getUnitParts'])->name('unit-transactions.get-unit-parts');
        Route::get('/data', [UnitTransactionController::class, 'getTransactions'])->name('unit-transactions.data');
        Route::get('/units', [UnitTransactionController::class, 'getUnits'])->name('unit-transactions.units');
        Route::get('/export-pdf', [UnitTransactionController::class, 'exportPdf'])->name('unit-transactions.export-pdf');
        Route::get('/preview-pdf', [UnitTransactionController::class, 'previewPdf'])->name('unit-transactions.preview-pdf');
        Route::get('/export-selected', [UnitTransactionController::class, 'exportSelected'])->name('unit-transactions.export-selected');
        Route::get('/preview-selected', [UnitTransactionController::class, 'previewSelected'])->name('unit-transactions.preview-selected');
        Route::get('/export-excel', [UnitTransactionController::class, 'exportExcel'])->name('unit-transactions.export-excel');
        Route::get('/export-selected-excel', [UnitTransactionController::class, 'exportSelectedExcel'])->name('unit-transactions.export-selected-excel');
        Route::get('/search-parts', [UnitTransactionController::class, 'searchParts'])->name('unit-transactions.search-parts');
        Route::post('/preview-bapp', [UnitTransactionController::class, 'previewBAPP'])->name('unit-transactions.preview-bapp');
        Route::get('/{id}', [UnitTransactionController::class, 'getTransaction'])->name('unit-transactions.get');
        Route::post('/', [UnitTransactionController::class, 'store'])->name('unit-transactions.store');
        Route::put('/{id}', [UnitTransactionController::class, 'update'])->name('unit-transactions.update');
        Route::delete('/{id}', [UnitTransactionController::class, 'destroy'])->name('unit-transactions.destroy');
    });

    }); // End of inventory mode middleware

    
    // Site Part Analysis Routes (available for all site users)
    Route::get('/sites/part-analysis', [App\Http\Controllers\Sites\PartAnalysisController::class, 'index'])->name('sites.part-analysis');
});

// Kasir Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/kasir/dashboard', [KasirController::class, 'dashboard'])->name('kasir.dashboard');
    Route::post('/kasir/transactions', [KasirController::class, 'store'])->name('kasir.transactions.store');
    Route::get('/kasir/transactions/data', [KasirController::class, 'getTransactions'])->name('kasir.transactions.data');
    Route::get('/kasir/summary', [KasirController::class, 'getSummary'])->name('kasir.summary');
});

// Test route
Route::get('/test-app', function() {
    return 'Application is working!';
});

// Debug route for kasir
Route::get('/debug-kasir', function() {
    $user = \Illuminate\Support\Facades\Auth::user();
    if (!$user) {
        return 'User not authenticated';
    }
    return response()->json([
        'user_id' => $user->employee_id,
        'role' => $user->role,
        'name' => $user->name,
        'is_kasir' => $user->role === 'kasir'
    ]);
});

