<?php

namespace App\Helpers;

class ClassifiedPart
{
    /**
     * Classify part demand based on stock-out history and current stock.
     *
     * @param array<int> $stockOutHistory  Monthly/Weekly stock-out quantities, e.g., [2, 3, 1, 0, 5, 6]
     * @param int        $stockNow         Current stock on hand
     *
     * @return string                      One of: 'High Demand', 'Stable', 'Low Demand', 'Seasonal', 'Overstocked', 'Uncategorized', 'Data Insufficient'
     */
    public static function classify(array $stockOutHistory, int $stockNow): string
    {
        // Constants (thresholds can be adjusted here)
        $MIN_REQUIRED_MONTHS = 4;
        $HIGH_DEMAND_AVG = 10;
        $STABLE_AVG_MIN = 1;
        $STABLE_AVG_MAX = 3;
        $LOW_DEMAND_AVG_MAX = 3;
        $OVERSTOCKED_MULTIPLIER = 6;

        $months = count($stockOutHistory);

        if ($months < $MIN_REQUIRED_MONTHS) {
            return 'Data Insufficient';
        }

        $averageUsage = array_sum($stockOutHistory) / $months;

        $variance = array_sum(array_map(
            fn($v) => pow($v - $averageUsage, 2),
            $stockOutHistory
        )) / $months;

        $stdDeviation = sqrt($variance);

        $usedMonths = count(array_filter($stockOutHistory, fn($v) => $v > 0));
        $usedPercent = $usedMonths / $months;

        // Classification Rules
        if (
            $averageUsage >= $HIGH_DEMAND_AVG &&
            $stdDeviation <= 2 &&
            $usedPercent > 0.8
        ) {
            return 'High Demand';
        }

        if (
            $averageUsage >= $STABLE_AVG_MIN &&
            $averageUsage <= $STABLE_AVG_MAX &&
            $usedPercent > 0.8 &&
            $stdDeviation <= 1.5
        ) {
            return 'Stable';
        }

        if (
            $usedPercent >= 0.7 &&
            $averageUsage < $LOW_DEMAND_AVG_MAX &&
            $stockNow < ($averageUsage * 2)
        ) {
            return 'Low Demand';
        }

        if (
            $stdDeviation > 5 &&
            $usedPercent <= 0.4 &&
            $averageUsage > 2
        ) {
            return 'Seasonal';
        }

        if (
            $stockNow > ($averageUsage * $OVERSTOCKED_MULTIPLIER) &&
            $averageUsage <= 1
        ) {
            return 'Overstocked';
        }

            return 'Uncategorized';
    }

    /**
     * Classify part demand based on stock-in/out history and current stock.
     *
     * @param array<int> $stockInHistory   Weekly stock-in quantities
     * @param array<int> $stockOutHistory  Weekly stock-out quantities
     * @param int        $stockNow         Current stock on hand
     *
     * @return string                      Classification result
     */
    public static function classifyWithInOut(array $stockInHistory, array $stockOutHistory, int $stockNow): string
    {
        $weeks = count($stockOutHistory);

        if ($weeks < 4 || count($stockInHistory) !== $weeks) {
            return 'Data Insufficient';
        }

        $totalIn = array_sum($stockInHistory);
        $totalOut = array_sum($stockOutHistory);
        $averageOut = $totalOut / $weeks;

        $usedWeeks = count(array_filter($stockOutHistory, fn($v) => $v > 0));
        $usedPercent = $usedWeeks / $weeks;

        // Calculate standard deviation for out usage
        $variance = array_sum(array_map(
            fn($v) => pow($v - $averageOut, 2),
            $stockOutHistory
        )) / $weeks;
        $stdDeviation = sqrt($variance);

        // === Classification Rules ===

        // Dormant: almost no usage
        if ($totalOut < 10 && $usedPercent <= 0.2) {
            return 'Dormant';
        }

        // Overstock: stock is excessive relative to usage
        if ($averageOut > 0 && $stockNow > ($averageOut * $weeks * 1.5)) {
            return 'Overstock';
        }

        // High Demand: consistent and more out than in
        if (
            $totalOut > $totalIn &&
            $usedPercent > 0.8 &&
            $stdDeviation <= 2 &&
            $averageOut >= 8
        ) {
            return 'High Demand';
        }

        // Stable: in ~ out, steady usage
        if (
            abs($totalIn - $totalOut) <= ($weeks * 1.5) &&
            $stdDeviation <= 2 &&
            $usedPercent > 0.6
        ) {
            return 'Stable';
        }

        // Low Demand: little usage but consistent
        if (
            $averageOut <= 2 &&
            $usedPercent >= 0.3 &&
            $stockNow <= ($averageOut * 2)
        ) {
            return 'Low Demand';
        }

        // Seasonal: big usage in a few months only
        if (
            $usedPercent <= 0.3 &&
            $averageOut >= 5 &&
            $stdDeviation > 10
        ) {
            return 'Seasonal';
        }

        // If none match
        return 'Uncategorized';
    }
}
