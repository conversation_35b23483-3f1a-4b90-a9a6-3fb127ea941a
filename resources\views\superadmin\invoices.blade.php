<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>Portal PWB - Invoice Management</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="Portal PWB" name="description" />
    <meta content="PWB" name="author" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link rel="shortcut icon" href="{{ asset('assets/images/logo-small.png') }}">
    <!-- Styles -->
    @vite([
    'resources/assets/css/bootstrap.min.css',
    'resources/assets/css/icons.min.css',
    'resources/assets/css/app.min.css',
    'resources/css/app.css',
    'resources/css/superadmin-dashboard.css',
    'resources/css/superadmin-scaling.css'
    ])
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jQuery-slimScroll/1.3.8/jquery.slimscroll.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/metisMenu/3.0.7/metisMenu.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Vite JS Resources -->
    @vite([
    'resources/js/superadmin-invoices.js',
    'resources/js/superadmin-scaling.js',
    'resources/js/superadmin-mobile-menu.js'
    ])
    <style>
        :root {
            --primary-color: #225297;
            --secondary-color: #58c0f6;
            --accent-color: #1e4785;
            --accent-hover-color: #3a6db5;
            --highlight-color: #e8f4ff;
            --danger-color: #eb3124;
            --success-color: #97f784;
            --info-color: #58c0f6;
            --warning-color: #feff8c;
            --text-color: #343a40;
            --text-muted: #6c757d;
            --border-color: rgba(0, 0, 0, 0.1);
            --card-bg-color: rgba(255, 255, 255, 0.95);
        }

        .btn:hover {
            border-radius: 50px;
            background: #e0e0e0;
            box-shadow: inset 20px 20px 60px var(--primary-color),
                inset -20px -20px 60px #ffffff;
        }

        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            background: url('{{ asset('assets/images/homewalpaper.jpg') }}');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            font-family: 'Segoe UI', sans-serif;
            position: relative;
            overflow-x: hidden;
        }

        .dashboard-container {
            width: 100%;
            max-width: 1600px;
            padding: 0 15px;
            margin: 0 auto;
        }

        .login-theme-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 100;
            margin-bottom: 20px;
            border-radius: 0 0 10px 10px;
            border-bottom: 3px solid var(--primary-color);
        }

        .header-top {
            display: none; /* Hide on desktop, only show on mobile */
        }

        .company-logo {
            display: flex;
            align-items: center;
        }

        .company-logo img {
            height: 40px;
            margin-right: 10px;
        }

        .company-name {
            font-size: 1.2rem;
            font-weight: 700;
            margin: 0;
            color: var(--primary-color);
        }

        /* Mobile Menu Toggle Button */
        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            color: var(--primary-color);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .mobile-menu-toggle:hover {
            background-color: rgba(42, 105, 168, 0.1);
        }

        /* Mobile Menu Close Button */
        .mobile-menu-close {
            display: none;
            background: none;
            border: none;
            color: var(--danger-color);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1001;
            transition: all 0.3s ease;
        }

        .mobile-menu-close:hover {
            background-color: rgba(42, 105, 168, 0.1);
        }

        /* Mobile Menu Overlay */
        .mobile-menu-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 998;
        }

        /* Mobile Menu Header */
        .mobile-menu-header {
            margin-bottom: 15px;
            padding-bottom: 10px;
            padding-top: 30px; /* Add padding to top to avoid overlap with close button */
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .menu-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--primary-color);
            margin: 0;
            padding: 0;
        }

        /* Header Right */
        .header-right {
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        /* Month Picker Container */
        .month-picker-container {
            margin-right: 15px;
        }

        /* Navigation Links */
        .nav-links {
            display: flex;
            flex-wrap: wrap;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 8px 15px;
            margin: 0 5px 5px 0;
            border-radius: 8px;
            text-decoration: none;
            color: var(--primary-color);
            background-color: rgba(42, 105, 168, 0.1);
            transition: all 0.3s ease;
            border: 1px solid var(--primary-color);
        }

        .nav-link i {
            margin-right: 8px;
            font-size: 1.1rem;
        }

        .nav-link:hover {
            background-color: var(--primary-color);
            color: #fff;
        }

        .nav-link.active {
            background-color: var(--primary-color);
            color: #fff;
        }

        .nav-link-danger {
            color: var(--danger-color) !important;
            background-color: rgba(255, 93, 72, 0.1) !important;
            border: 1px solid var(--danger-color) !important;
        }

        .nav-link-danger:hover {
            background-color: var(--danger-color) !important;
            color: #fff !important;
        }

        .card {
            background-color: var(--card-bg-color) !important;
            border-radius: 10px !important;
            border: 1px solid var(--border-color) !important;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: 1rem;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background-color: rgba(42, 105, 168, 0.05) !important;
            border-bottom: 1px solid var(--border-color) !important;
            padding: 0.75rem 1rem;
        }

        .card-title {
            margin: 0;
            color: var(--text-color) !important;
            font-weight: 600;
            font-size: 1rem;
        }

        .card-body {
            padding: 1rem;
        }

        .form-control {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-color);
            font-size: 1em;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
        }

        .form-control:focus {
            outline: none;
            background: #ffffff;
            color: var(--text-color);
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(42, 105, 168, 0.25);
        }

        .btn {
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-outline-primary {
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
            background: rgba(34, 82, 151, 0.1);
        }

        .btn-outline-primary:hover {
            border-radius: 50px;
            box-shadow: inset 20px 20px 60px rgb(252, 251, 251),
                inset -20px -20px 60px #ffffff;
            background: var(--primary-color);
            color: #ffffff;
            border-color: var(--primary-color);
        }

        .btn-primary {
            border-radius: 50px;
            box-shadow: inset 20px 20px 60px #1e4785,
                inset -20px -20px 60px #3a6db5;
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: #ffffff;
        }

        .btn-primary:hover {
            background: var(--accent-color);
            border-color: var(--accent-color);
            color: #ffffff;
        }

        .content-wrapper {
            padding: 10px 5px;
            max-width: 100%;
            margin: 0 auto;
        }

        .table th {
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
            border: none;
        }

        .table-hover tbody tr:hover {
            background-color: var(--highlight-color);
        }

        .table td {
            vertical-align: middle;
        }

        .search-box {
            position: relative;
            margin-bottom: 0;
        }

        .search-box input {
            border-radius: 20px;
            padding-left: 40px;
            border: 1px solid var(--border-color);
        }

        .search-box .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
        }

        .invoice-link {
            color: var(--primary-color);
            font-weight: 600;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .invoice-link:hover {
            color: var(--secondary-color);
            text-decoration: underline;
        }

        /* Responsive styles */
        @media (max-width: 992px) {
            .login-theme-header {
                padding: 0;
                flex-direction: column;
                align-items: stretch;
            }

            /* Hide desktop logo on mobile */
            .login-theme-header > .company-logo {
                display: none;
            }

            /* Show header-top on mobile */
            .header-top {
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
                padding: 10px 15px;
            }

            /* Show mobile menu toggle button */
            .mobile-menu-toggle {
                display: block;
            }

            /* Mobile menu styling */
            .header-right {
                position: fixed;
                top: 0; /* Position at the top */
                right: -100%; /* Hide off-screen initially using percentage */
                width: 85%; /* Use percentage width to ensure it fits on all screens */
                max-width: 300px; /* Set a maximum width */
                height: auto; /* Auto height instead of 100% */
                max-height: 80vh; /* Maximum height of 80% of viewport height */
                background-color: #fff;
                box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
                padding: 15px;
                z-index: 999;
                overflow-y: auto;
                transition: right 0.3s ease;
                flex-direction: column;
                border-radius: 0 0 0 10px; /* Rounded corners on bottom left */
            }

            /* Show mobile menu close button */
            .mobile-menu-close {
                display: block;
            }

            /* When mobile menu is active */
            .header-right.active {
                right: 0;
            }

            /* Show overlay when mobile menu is active */
            .mobile-menu-overlay.active {
                display: block;
            }

            /* Navigation links in mobile view */
            .nav-links {
                flex-direction: column;
                width: 100%;
                margin-top: 20px;
            }

            .nav-link {
                width: 100%;
                margin: 0 0 10px 0;
                padding: 12px 15px;
                justify-content: flex-start;
            }

            .nav-link i {
                width: 24px;
                text-align: center;
                margin-right: 10px;
            }
        }

        /* Mobile specific styles */
        @media (max-width: 576px) {
            .login-theme-header {
                padding: 8px 12px;
            }

            .company-logo {
                flex-direction: row;
                align-items: center;
            }

            .company-logo img {
                height: 28px;
                margin-right: 6px;
                margin-bottom: 0;
            }

            .company-name {
                font-size: 0.8rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 160px;
            }

            .mobile-menu-toggle {
                padding: 5px;
                font-size: 1.3rem;
            }

            .mobile-menu-close {
                padding: 5px;
                font-size: 1.3rem;
                top: 5px;
                right: 5px;
            }

            .header-right {
                width: 85%;
                max-width: 280px;
                padding: 10px;
            }

            .nav-links {
                margin-top: 15px;
            }

            .nav-link {
                padding: 8px 10px;
                font-size: 0.85rem;
                margin-bottom: 8px;
            }

            .nav-link i {
                font-size: 1rem;
                margin-right: 6px;
                width: 20px;
            }
        }
    </style>
</head>

<body>
    <!-- Dashboard Container -->
    <div class="dashboard-container">
        <!-- Login Theme Header -->
        <header class="login-theme-header">
            <!-- Company Logo (Visible on all devices) -->
            <div class="company-logo">
                <img src="{{ asset('assets/images/logo-small.png') }}" alt="PWB Logo">
                <h1 class="company-name">PT. PUTERA WIBOWO BORNEO</h1>
            </div>

            <!-- Mobile Header Top (Only visible on mobile) -->
            <div class="header-top">
                <div class="company-logo">
                    <img src="{{ asset('assets/images/logo-small.png') }}" alt="PWB Logo">
                    <h1 class="company-name">PT. PUTERA WIBOWO BORNEO</h1>
                </div>

                <!-- Mobile Menu Toggle Button -->
                <button type="button" class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="mdi mdi-menu"></i>
                </button>
            </div>

            <!-- Mobile Menu Overlay -->
            <div class="mobile-menu-overlay" id="mobileMenuOverlay"></div>

            <!-- Header Right (Navigation) -->
            <div class="header-right" id="mobileMenu">
                <!-- Close Button for Mobile Menu -->
                <button type="button" class="mobile-menu-close" id="mobileMenuClose">
                    <i class="mdi mdi-close"></i>
                </button>

                <!-- Mobile Menu Header (Only visible on mobile) -->
                <div class="mobile-menu-header d-lg-none">
                    <h5 class="menu-title">Menu Navigasi</h5>
                </div>

                <!-- Month Picker -->
                <div class="month-picker-container mb-3 mb-md-0 me-md-3">
                    <form id="monthForm" class="d-flex align-items-center">
                        <div class="d-flex align-items-center">
                            <!-- Previous Month Button -->
                            <button type="button" class="btn btn-sm btn-outline-primary" id="prevMonthBtn">
                                <i class="mdi mdi-chevron-left"></i>
                            </button>

                            <!-- Month Input -->
                            <input type="month" id="monthPicker" name="month" class="form-control form-control-sm mx-2" value="{{ $selectedMonth ?? now()->format('Y-m') }}">

                            <!-- Next Month Button -->
                            <button type="button" class="btn btn-sm btn-outline-primary" id="nextMonthBtn">
                                <i class="mdi mdi-chevron-right"></i>
                            </button>
                        </div>
                    </form>
                </div>

                <div class="nav-links">
                    <a href="{{ route('superadmin.dashboard') }}" class="nav-link {{ request()->routeIs('superadmin.dashboard') ? 'active' : '' }}">
                        <i class="mdi mdi-view-dashboard"></i> <span>Dashboard</span>
                    </a>
                    <a href="{{ route('superadmin.invoices') }}" class="nav-link {{ request()->routeIs('superadmin.invoices') ? 'active' : '' }}">
                        <i class="mdi mdi-file-document-outline"></i> <span>acount receveable</span>
                    </a>
                    <a href="{{ route('superadmin.parts') }}" class="nav-link {{ request()->routeIs('superadmin.parts') ? 'active' : '' }}">
                        <i class="mdi mdi-package-variant"></i> <span>Part</span>
                    </a>
                    <a href="{{ route('superadmin.part-analysis') }}" class="nav-link {{ request()->routeIs('superadmin.part-analysis') ? 'active' : '' }}">
                        <i class="mdi mdi-chart-line"></i> <span>Part Analysis</span>
                    </a>
                    <a href="{{ route('superadmin.price-list') }}" class="nav-link {{ request()->routeIs('superadmin.price-list') ? 'active' : '' }}">
                        <i class="mdi mdi-tag-multiple"></i> <span>Price List</span>
                    </a>
                    <a href="{{ route('logout') }}" class="nav-link nav-link-danger">
                        <i class="mdi mdi-logout"></i> <span>Logout</span>
                    </a>
                </div>
            </div>
        </header>

        <!-- Skeleton Loader -->
        <div id="skeleton-loader" class="skeleton-container" style="display: block; padding-top: 100px;">
            <div class="container-fluid py-4">
                <!-- Skeleton for Invoice Table -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="skeleton-card">
                            <div class="skeleton-header"></div>
                            <div class="skeleton-body">
                                <div class="skeleton-line" style="height: 40px;"></div>
                                <div class="skeleton-line"></div>
                                <div class="skeleton-line"></div>
                                <div class="skeleton-line"></div>
                                <div class="skeleton-line"></div>
                                <div class="skeleton-line"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!-- Invoice Detail Modal -->
        <div class="modal fade" id="invoice-detail-modal" tabindex="-1" aria-labelledby="invoice-detail-modal-label" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header" style="background-color: var(--primary-color); color: white;">
                        <h5 class="modal-title text-white" id="invoice-detail-modal-label">Detail Invoice</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div id="invoice-detail-content">
                            <!-- Invoice detail content will be populated by JavaScript -->
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Document Viewer Modal -->
        <div class="modal fade" id="document-viewer-modal" tabindex="-1" aria-labelledby="document-viewer-modal-label" aria-hidden="true">
            <div class="modal-dialog modal-fullscreen">
                <div class="modal-content">
                    <div class="modal-header" style="background-color: var(--primary-color); color: white;">
                        <h5 class="modal-title text-white" id="document-viewer-modal-label">Lampiran Invoice</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-0">
                        <div id="document-viewer-content" style="height: 90vh; width: 100%;">
                            <!-- Document viewer content will be populated by JavaScript -->
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ready PO Attachment Viewer Modal -->
        <div class="modal fade" id="ready-po-attachment-modal" tabindex="-1" aria-labelledby="ready-po-attachment-modal-label" aria-hidden="true">
            <div class="modal-dialog modal-fullscreen">
                <div class="modal-content">
                    <div class="modal-header" style="background-color: var(--warning-color); color: black;">
                        <h5 class="modal-title text-dark" id="ready-po-attachment-modal-label">Lampiran Unit Transaction</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-0">
                        <div id="ready-po-attachment-content" style="height: 90vh; width: 100%;">
                            <!-- Ready PO attachment content will be populated by JavaScript -->
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                    </div>
                </div>
            </div>
        </div>

         <!-- Invoices Section -->
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="card-title mb-0">
                            <i class="mdi mdi-file-document-outline me-1"></i> Daftar Invoice
                        </h4>
                        <div class="d-flex">
                            <select id="site-filter" class="btn btn-sm btn-success form-select-sm me-2">
                                <option value="">Semua Site</option>
                                <!-- Site options will be populated by JavaScript -->
                            </select>
                            <select id="status-filter" class="btn btn-sm btn-success form-select form-select-sm me-2">
                                <option value="">Semua Status</option>
                                <option value="Lunas">Lunas</option>
                                <option value="Belum Lunas">Belum Lunas</option>
                                <option value="Jatuh Tempo">Jatuh Tempo</option>
                            </select>
                            <div class="search-box">
                                <i class="mdi mdi-magnify search-icon"></i>
                                <input type="text" id="search-input" class="form-control form-control-sm" placeholder="Cari invoice...">
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>No. Invoice</th>
                                    <th>Site</th>
                                    <th>Customer</th>
                                    <th>Tanggal</th>
                                    <th>Nilai Invoice</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="invoice-table-body">
                                <!-- Invoice data will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div class="pagination-info">
                            Menampilkan <span id="pagination-start">0</span> - <span id="pagination-end">0</span> dari <span id="pagination-total">0</span> invoice
                        </div>
                        <div class="pagination-controls">
                            <button id="prev-page" class="btn btn-sm btn-outline-primary me-2">
                                <i class="mdi mdi-chevron-left"></i> Sebelumnya
                            </button>
                            <button id="next-page" class="btn btn-sm btn-outline-primary">
                                Selanjutnya <i class="mdi mdi-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

                <!-- Content Wrapper -->
        <div class="content-wrapper" style="display: none;">
            <!-- Ready PO Transactions Section -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="card-title mb-0">
                            <i class="mdi mdi-clipboard-check-outline me-1"></i> Ready PO
                        </h4>
                        <div class="d-flex">
                            <select id="ready-po-site-filter" class="btn btn-sm btn-success form-select-sm me-2">
                                <option value="">All Sites</option>
                                <option value="PPA">PPA</option>
                                <option value="UDU">UDU</option>
                                <option value="IMK">IMK</option>
                                <option value="WHO">WHO</option>
                            </select>
                            <div class="search-box">
                                <i class="mdi mdi-magnify search-icon"></i>
                                <input type="text" id="ready-po-search-input" class="form-control form-control-sm" placeholder="Cari Ready PO...">
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>No PO</th>
                                    <th>Unit Code</th>
                                    <th>Unit Type</th>
                                    <th>Site</th>
                                    <th>Customer</th>
                                    <th>Tanggal Update</th>
                                    <th>Total Value</th>
                                </tr>
                            </thead>
                            <tbody id="ready-po-table-body">
                                <!-- Ready PO data will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Ready PO Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div class="pagination-info">
                            Menampilkan <span id="ready-po-pagination-start">0</span> - <span id="ready-po-pagination-end">0</span> dari <span id="ready-po-pagination-total">0</span> transaksi Ready PO
                        </div>
                        <div class="pagination-controls">
                            <button id="ready-po-prev-page" class="btn btn-sm btn-outline-primary me-2">
                                <i class="mdi mdi-chevron-left"></i> Sebelumnya
                            </button>
                            <button id="ready-po-next-page" class="btn btn-sm btn-outline-primary">
                                Selanjutnya <i class="mdi mdi-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>
</body>

</html>
